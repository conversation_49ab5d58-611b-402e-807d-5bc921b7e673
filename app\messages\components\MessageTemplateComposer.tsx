"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import {
  Send,
  Edit3,
  X,
  AlertCircle,
  CheckCircle,
  Sparkles,
  Lock,
  Globe,
  Users,
  Eye,
  EyeOff,
  Shield
} from "lucide-react"
import { cn } from "@/lib/utils"
import { MessageTemplate } from '@/types/message-templates'
import { detectSensitiveData, getPrivacyRecommendations } from '@/lib/privacy-protection'

interface Context {
  order_id?: string | null
  business_id?: string | null
  rider_id?: string | null
  order_number?: string
  business_name?: string
  rider_name?: string
}

interface MessageTemplateComposerProps {
  template: MessageTemplate
  context?: Context
  onSend: (content: string, template: MessageTemplate, privacySettings?: PrivacySettings) => void
  onCancel: () => void
  className?: string
}

interface PrivacySettings {
  is_public: boolean
  allows_anyone_to_answer: boolean
  hide_sensitive_data: boolean
}

export function MessageTemplateComposer({
  template,
  context,
  onSend,
  onCancel,
  className
}: MessageTemplateComposerProps) {
  const [messageContent, setMessageContent] = useState('')
  const [variables, setVariables] = useState<Record<string, string>>({})
  const [isValid, setIsValid] = useState(false)
  const [missingVariables, setMissingVariables] = useState<string[]>([])

  // Privacy controls
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>({
    is_public: false,
    allows_anyone_to_answer: false,
    hide_sensitive_data: true
  })
  const [showPrivacyOptions, setShowPrivacyOptions] = useState(false)

  useEffect(() => {
    initializeTemplate()
  }, [template, context])

  useEffect(() => {
    validateAndUpdateContent()
  }, [variables, template])

  const initializeTemplate = () => {
    // Auto-populate variables from context
    const initialVariables: Record<string, string> = {}

    if (template.variables) {
      template.variables.forEach(variable => {
        // Try to auto-populate from context
        switch (variable) {
          case 'order_number':
            initialVariables[variable] = context?.order_number || context?.order_id?.slice(-6) || ''
            break
          case 'business_name':
            initialVariables[variable] = context?.business_name || ''
            break
          case 'rider_name':
            initialVariables[variable] = context?.rider_name || ''
            break
          default:
            initialVariables[variable] = ''
        }
      })
    }

    setVariables(initialVariables)
  }

  const validateAndUpdateContent = () => {
    let content = template.content
    const missing: string[] = []

    if (template.variables) {
      template.variables.forEach(variable => {
        const value = variables[variable]
        if (value && value.trim()) {
          // Replace variable placeholder with actual value
          content = content.replace(new RegExp(`{${variable}}`, 'g'), value)
        } else {
          // Mark as missing
          missing.push(variable)
        }
      })
    }

    setMessageContent(content)
    setMissingVariables(missing)
    setIsValid(missing.length === 0)
  }

  const handleVariableChange = (variable: string, value: string) => {
    setVariables(prev => ({
      ...prev,
      [variable]: value
    }))
  }

  const handleSend = () => {
    if (isValid) {
      onSend(messageContent, template, privacySettings)
    }
  }

  const getPrivacyIcon = () => {
    if (privacySettings.is_public) {
      return privacySettings.allows_anyone_to_answer ? <Users className="h-4 w-4" /> : <Globe className="h-4 w-4" />
    }
    return <Lock className="h-4 w-4" />
  }

  const getPrivacyLabel = () => {
    if (privacySettings.is_public) {
      return privacySettings.allows_anyone_to_answer ? 'Public - Anyone can answer' : 'Public - View only'
    }
    return 'Private conversation'
  }

  const getPrivacyDescription = () => {
    if (privacySettings.is_public) {
      if (privacySettings.allows_anyone_to_answer) {
        return 'This message will be visible to the community and anyone can join the conversation.'
      }
      return 'This message will be visible to the community but only you and the recipient can respond.'
    }
    return 'This message will only be visible to you and the recipient.'
  }

  const shouldShowSensitiveDataWarning = () => {
    if (!privacySettings.is_public) return false

    const detection = detectSensitiveData(messageContent, context)
    return detection.hasSensitiveData
  }

  const getPrivacyRecommendationsForMessage = () => {
    return getPrivacyRecommendations(messageContent, context)
  }

  const getVariableLabel = (variable: string): string => {
    switch (variable) {
      case 'order_number':
        return 'Order Number'
      case 'business_name':
        return 'Business Name'
      case 'rider_name':
        return 'Driver Name'
      case 'delay_minutes':
        return 'Delay (minutes)'
      case 'eta_minutes':
        return 'ETA (minutes)'
      case 'item_name':
        return 'Item Name'
      case 'allergen_list':
        return 'Allergens'
      case 'issue_description':
        return 'Issue Description'
      case 'refund_reason':
        return 'Refund Reason'
      case 'user_name':
        return 'Your Name'
      default:
        return variable.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
    }
  }

  const getVariablePlaceholder = (variable: string): string => {
    switch (variable) {
      case 'order_number':
        return 'e.g., 123456'
      case 'business_name':
        return 'e.g., Pizza Palace'
      case 'delay_minutes':
        return 'e.g., 15'
      case 'eta_minutes':
        return 'e.g., 20'
      case 'item_name':
        return 'e.g., Margherita Pizza'
      case 'allergen_list':
        return 'e.g., nuts, dairy'
      case 'issue_description':
        return 'Describe the issue...'
      case 'refund_reason':
        return 'Reason for refund...'
      case 'user_name':
        return 'Your name'
      default:
        return `Enter ${variable.replace(/_/g, ' ')}`
    }
  }

  return (
    <Card className={cn("w-full max-w-md mx-auto", className)}>
      <CardHeader className="pb-4">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-emerald-600" />
            {template.title}
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            className="h-8 w-8 p-0"
          >
            <X className="h-4 w-4" />
          </Button>
        </div>
        {template.is_urgent && (
          <Badge variant="destructive" className="w-fit">
            <AlertCircle className="h-3 w-3 mr-1" />
            Urgent
          </Badge>
        )}
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Variable inputs */}
        {template.variables && template.variables.length > 0 && (
          <div className="space-y-3">
            <Label className="text-sm font-medium text-gray-700">
              Fill in the details:
            </Label>
            {template.variables.map(variable => (
              <div key={variable} className="space-y-1">
                <Label htmlFor={variable} className="text-xs text-gray-600">
                  {getVariableLabel(variable)}
                  {missingVariables.includes(variable) && (
                    <span className="text-red-500 ml-1">*</span>
                  )}
                </Label>
                <Input
                  id={variable}
                  value={variables[variable] || ''}
                  onChange={(e) => handleVariableChange(variable, e.target.value)}
                  placeholder={getVariablePlaceholder(variable)}
                  className={cn(
                    "text-sm",
                    missingVariables.includes(variable) && "border-red-300 focus:border-red-500"
                  )}
                />
              </div>
            ))}
          </div>
        )}

        {/* Privacy Controls */}
        <div className="space-y-3 border-t pt-4">
          <div className="flex items-center justify-between">
            <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Privacy Settings
            </Label>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPrivacyOptions(!showPrivacyOptions)}
              className="h-8 px-2 text-xs"
            >
              {showPrivacyOptions ? <EyeOff className="h-3 w-3" /> : <Eye className="h-3 w-3" />}
              {showPrivacyOptions ? 'Hide' : 'Show'} Options
            </Button>
          </div>

          {/* Privacy Status Display */}
          <div className={cn(
            "flex items-center gap-2 p-3 rounded-lg border",
            privacySettings.is_public
              ? "bg-blue-50 border-blue-200 text-blue-800"
              : "bg-gray-50 border-gray-200 text-gray-800"
          )}>
            {getPrivacyIcon()}
            <div className="flex-1">
              <div className="font-medium text-sm">{getPrivacyLabel()}</div>
              <div className="text-xs opacity-75">{getPrivacyDescription()}</div>
            </div>
          </div>

          {/* Privacy Options */}
          {showPrivacyOptions && (
            <div className="space-y-3 p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <Label className="text-sm font-medium">Make this message public</Label>
                  <p className="text-xs text-gray-600 mt-1">
                    Public messages are visible to the community and can help others with similar questions.
                  </p>
                </div>
                <Button
                  variant={privacySettings.is_public ? "default" : "outline"}
                  size="sm"
                  onClick={() => setPrivacySettings(prev => ({
                    ...prev,
                    is_public: !prev.is_public,
                    allows_anyone_to_answer: prev.is_public ? false : prev.allows_anyone_to_answer
                  }))}
                  className="ml-3"
                >
                  {privacySettings.is_public ? <Globe className="h-3 w-3 mr-1" /> : <Lock className="h-3 w-3 mr-1" />}
                  {privacySettings.is_public ? 'Public' : 'Private'}
                </Button>
              </div>

              {privacySettings.is_public && (
                <div className="flex items-center justify-between pl-4 border-l-2 border-blue-200">
                  <div className="flex-1">
                    <Label className="text-sm font-medium">Allow anyone to answer</Label>
                    <p className="text-xs text-gray-600 mt-1">
                      Let community members join the conversation and help answer your question.
                    </p>
                  </div>
                  <Button
                    variant={privacySettings.allows_anyone_to_answer ? "default" : "outline"}
                    size="sm"
                    onClick={() => setPrivacySettings(prev => ({
                      ...prev,
                      allows_anyone_to_answer: !prev.allows_anyone_to_answer
                    }))}
                    className="ml-3"
                  >
                    {privacySettings.allows_anyone_to_answer ? <Users className="h-3 w-3 mr-1" /> : <Globe className="h-3 w-3 mr-1" />}
                    {privacySettings.allows_anyone_to_answer ? 'Anyone' : 'View Only'}
                  </Button>
                </div>
              )}
            </div>
          )}

          {/* Sensitive Data Warning */}
          {shouldShowSensitiveDataWarning() && (
            <div className="flex items-start gap-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
              <AlertCircle className="h-4 w-4 text-amber-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800">
                <div className="font-medium">Privacy Alert</div>
                <div className="text-xs mt-1 space-y-1">
                  {getPrivacyRecommendationsForMessage().map((recommendation, index) => (
                    <div key={index}>• {recommendation}</div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Privacy Recommendations for Public Messages */}
          {privacySettings.is_public && !shouldShowSensitiveDataWarning() && (
            <div className="flex items-start gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
              <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-green-800">
                <div className="font-medium">Great for public sharing!</div>
                <div className="text-xs mt-1">
                  This message looks safe to share publicly and could help others in the community.
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Message preview */}
        <div className="space-y-2">
          <Label className="text-sm font-medium text-gray-700 flex items-center gap-2">
            <Edit3 className="h-4 w-4" />
            Message Preview:
          </Label>
          <Textarea
            value={messageContent}
            onChange={(e) => setMessageContent(e.target.value)}
            className="min-h-[100px] text-sm"
            placeholder="Your message will appear here..."
          />
        </div>

        {/* Validation status */}
        {missingVariables.length > 0 && (
          <div className="flex items-center gap-2 text-sm text-red-600 bg-red-50 p-2 rounded">
            <AlertCircle className="h-4 w-4" />
            Please fill in: {missingVariables.map(v => getVariableLabel(v)).join(', ')}
          </div>
        )}

        {isValid && (
          <div className="flex items-center gap-2 text-sm text-green-600 bg-green-50 p-2 rounded">
            <CheckCircle className="h-4 w-4" />
            Message ready to send
          </div>
        )}

        {/* Action buttons */}
        <div className="flex gap-2 pt-2">
          <Button
            onClick={handleSend}
            disabled={!isValid}
            className="flex-1"
          >
            <Send className="h-4 w-4 mr-2" />
            Send Message
          </Button>
          <Button
            variant="outline"
            onClick={onCancel}
            className="px-4"
          >
            Cancel
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
