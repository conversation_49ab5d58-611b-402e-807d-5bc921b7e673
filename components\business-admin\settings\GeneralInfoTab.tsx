"use client"

import { useState, useEffect, memo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { Store, HelpCircle, CheckCircle, AlertCircle } from "lucide-react"
import type { BusinessSettingsFormData } from "@/hooks/use-business-settings-form"

interface GeneralInfoTabProps {
  formData: BusinessSettingsFormData
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  handleSelectChange: (name: string, value: string) => void
  businessTypes: Array<{ id: number; name: string }>
}

export const GeneralInfoTab = memo(function GeneralInfoTab({
  formData,
  handleChange,
  handleSelectChange,
  businessTypes
}: GeneralInfoTabProps) {
  const handleBusinessTypeChange = (value: string) => {
    handleSelectChange('business_type_id', value)
  }



  // Find the selected business type name for display
  const selectedBusinessType = businessTypes.find(type => type.id === formData.business_type_id)
  const selectedBusinessTypeName = selectedBusinessType?.name || ""

  const isEssentialComplete = formData.name && formData.description && formData.phone

  return (
    <div className="space-y-8">
      {/* Essential Information Section */}
      <Card className="border border-gray-200">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gray-800 rounded-lg p-2">
                <Store className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Essential Information</CardTitle>
                <CardDescription className="text-gray-600 text-sm">
                  Basic details that customers will see when browsing your business
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {isEssentialComplete ? (
                <div className="flex items-center space-x-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-xs font-medium">Complete</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1 text-amber-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-xs font-medium">Incomplete</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          {/* Business Type Selector */}
          <div className="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center space-x-2 mb-3">
              <Label className="text-sm font-medium text-gray-700 flex items-center">
                Business Type
                <span className="text-red-500 ml-1">*</span>
              </Label>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">Selecting your business type helps us show relevant categories and features. This affects what options appear below.</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <Select
              value={formData.business_type_id?.toString() || ""}
              onValueChange={handleBusinessTypeChange}
            >
              <SelectTrigger className="bg-white border-blue-300 focus:border-blue-500">
                <SelectValue
                  placeholder="Select your business type"
                >
                  {selectedBusinessTypeName || "Select your business type"}
                </SelectValue>
              </SelectTrigger>
              <SelectContent>
                {businessTypes.map((type) => (
                  <SelectItem key={type.id} value={type.id.toString()}>
                    {type.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="name" className="text-sm font-medium text-gray-700 flex items-center">
                  Business Name
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">This is the name customers will see when searching for your business. Make it clear and memorable.</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                placeholder="e.g. Jersey Medical Centre, Island Pharmacy, St. Helier Bakery"
                className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="phone" className="text-sm font-medium text-gray-700 flex items-center">
                  Phone Number
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">Customers will use this number to contact you directly. Include your area code.</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Input
                id="phone"
                name="phone"
                value={formData.phone}
                onChange={handleChange}
                placeholder="e.g. ************"
                className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>
          </div>

          <div className="mt-6 space-y-2">
            <div className="flex items-center space-x-2">
              <Label htmlFor="description" className="text-sm font-medium text-gray-700 flex items-center">
                Business Description
                <span className="text-red-500 ml-1">*</span>
              </Label>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">Write a compelling description that highlights what makes your business special. This appears in search results and on your business page.</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <Textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleChange}
              placeholder="Describe your business, what you offer, and what makes you special..."
              rows={4}
              className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
})
