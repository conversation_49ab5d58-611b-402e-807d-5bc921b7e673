'use client';

import { useEffect, useState, useRef } from 'react';
import { useAuth } from '@/context/unified-auth-context';
import { synchronizeAuthState } from '@/utils/auth-sync';

/**
 * AuthSynchronizer component
 *
 * This component synchronizes the authentication state on page load.
 * It should be placed near the root of your application.
 */
export default function AuthSynchronizer() {
  const { user, isLoading } = useAuth();
  const [hasSynced, setHasSynced] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);
  const initialSyncDone = useRef(false);
  const previousUserRef = useRef(user);

  // Debug: Track how many times this component re-renders
  const renderCount = useRef(0);
  renderCount.current += 1;
  console.log(`🔄 AuthSynchronizer render #${renderCount.current}`, {
    user: user?.email,
    isLoading,
    hasSynced,
    isSyncing
  });

  // Listen for auth-related events
  useEffect(() => {
    const handleAuthEvent = () => {
      try {
        console.log("AuthSynchronizer: Auth event detected, will sync");
        setHasSynced(false); // Reset sync state to trigger a new sync
      } catch (error) {
        console.error("AuthSynchronizer: Error handling auth event:", error);
      }
    };

    const handleLoginSuccess = (event: Event) => {
      try {
        console.log("AuthSynchronizer: Login success event detected");
        handleAuthEvent();
      } catch (error) {
        console.error("AuthSynchronizer: Error handling login success event:", error);
      }
    };

    const handleSyncRequested = () => {
      try {
        console.log("AuthSynchronizer: Sync requested event detected");
        handleAuthEvent();
      } catch (error) {
        console.error("AuthSynchronizer: Error handling sync requested event:", error);
      }
    };

    const handleStorageEvent = (event: StorageEvent) => {
      try {
        if (event.key && (
          event.key === 'loop_jersey_auth_token' ||
          event.key === 'loop_jersey_session_active' ||
          event.key === 'loop_jersey_user'
        )) {
          console.log(`AuthSynchronizer: Storage event for ${event.key}, will sync`);
          handleAuthEvent();
        }
      } catch (error) {
        console.error("AuthSynchronizer: Error handling storage event:", error);
      }
    };

    // Listen for custom auth events with proper error handling
    window.addEventListener('auth-token-refreshed', handleAuthEvent);
    window.addEventListener('auth-login-success', handleLoginSuccess);
    window.addEventListener('auth-sync-requested', handleSyncRequested);
    window.addEventListener('storage', handleStorageEvent);

    return () => {
      window.removeEventListener('auth-token-refreshed', handleAuthEvent);
      window.removeEventListener('auth-login-success', handleLoginSuccess);
      window.removeEventListener('auth-sync-requested', handleSyncRequested);
      window.removeEventListener('storage', handleStorageEvent);
    };
  }, []);

  // Synchronize auth state on component mount - but only once
  useEffect(() => {
    const syncAuth = async () => {
      try {
        // Skip if we've already synced or if we're currently syncing
        if (initialSyncDone.current || isSyncing) return;

        // Check if we're a super admin with valid token - if so, skip aggressive sync
        if (typeof window !== 'undefined') {
          try {
            const hasToken = localStorage.getItem('loop_jersey_auth_token');
            const storedUser = localStorage.getItem('loop_jersey_user');
            if (hasToken && storedUser) {
              const parsedUser = JSON.parse(storedUser);
              if (parsedUser?.email === '<EMAIL>') {
                console.log("AuthSynchronizer: Super admin with valid token, skipping initial sync");
                initialSyncDone.current = true;
                setHasSynced(true);
                return;
              }
            }
          } catch (error) {
            console.error("AuthSynchronizer: Error checking super admin status:", error);
            // Continue with normal sync if check fails
          }
        }

        try {
          setIsSyncing(true);
          // Initial authentication state sync
          await synchronizeAuthState();
          initialSyncDone.current = true;
          setHasSynced(true);
        } catch (error) {
          console.error("AuthSynchronizer: Error synchronizing authentication state:", error);
        } finally {
          setIsSyncing(false);
        }
      } catch (error) {
        console.error("AuthSynchronizer: Unexpected error in syncAuth:", error);
        setIsSyncing(false);
      }
    };

    // Wrap the async call to prevent unhandled promise rejection
    syncAuth().catch((error) => {
      console.error("AuthSynchronizer: Unhandled error in syncAuth:", error);
    });
  }, [isSyncing]); // Removed isLoading from dependency array to prevent infinite loops

  // If auth state changes significantly (user logs in or out), sync again
  useEffect(() => {
    try {
      // Only run this effect if the user state has actually changed
      const userChanged = (
        (!previousUserRef.current && user) || // User logged in
        (previousUserRef.current && !user) || // User logged out
        (previousUserRef.current?.id !== user?.id) // Different user
      );

      previousUserRef.current = user;

      if (userChanged && !isSyncing && initialSyncDone.current) {
        const syncAuthOnChange = async () => {
          try {
            setIsSyncing(true);
            console.log("AuthSynchronizer: Re-synchronizing due to significant auth state change");
            await synchronizeAuthState();
          } catch (error) {
            console.error("AuthSynchronizer: Error re-synchronizing authentication state:", error);
          } finally {
            setIsSyncing(false);
          }
        };

        // Wrap the async call to prevent unhandled promise rejection
        syncAuthOnChange().catch((error) => {
          console.error("AuthSynchronizer: Unhandled error in syncAuthOnChange:", error);
        });
      }
    } catch (error) {
      console.error("AuthSynchronizer: Error in user change effect:", error);
    }
  }, [user, isSyncing]);

  // If hasSynced is reset to false (due to an auth event), perform a sync
  useEffect(() => {
    try {
      if (!hasSynced && !isSyncing && initialSyncDone.current) {
        const syncAuthAfterEvent = async () => {
          try {
            // Check if we're a super admin with valid token - if so, skip sync
            if (typeof window !== 'undefined') {
              try {
                const hasToken = localStorage.getItem('loop_jersey_auth_token');
                const storedUser = localStorage.getItem('loop_jersey_user');
                if (hasToken && storedUser) {
                  const parsedUser = JSON.parse(storedUser);
                  if (parsedUser?.email === '<EMAIL>') {
                    console.log("AuthSynchronizer: Super admin with valid token, skipping event sync");
                    setHasSynced(true);
                    return;
                  }
                }
              } catch (error) {
                console.error("AuthSynchronizer: Error checking super admin status for event sync:", error);
                // Continue with normal sync if check fails
              }
            }

            try {
              setIsSyncing(true);
              console.log("AuthSynchronizer: Synchronizing after auth event");
              await synchronizeAuthState();
              setHasSynced(true);
            } catch (error) {
              console.error("AuthSynchronizer: Error synchronizing after auth event:", error);
            } finally {
              setIsSyncing(false);
            }
          } catch (error) {
            console.error("AuthSynchronizer: Unexpected error in syncAuthAfterEvent:", error);
            setIsSyncing(false);
          }
        };

        // Wrap the async call to prevent unhandled promise rejection
        syncAuthAfterEvent().catch((error) => {
          console.error("AuthSynchronizer: Unhandled error in syncAuthAfterEvent:", error);
        });
      }
    } catch (error) {
      console.error("AuthSynchronizer: Error in hasSynced effect:", error);
    }
  }, [hasSynced, isSyncing]);

  // This component doesn't render anything
  return null;
}
