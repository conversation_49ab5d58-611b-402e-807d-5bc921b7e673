import * as React from "react"
import type { Metadata, Viewport } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { RealtimeCartProvider } from "@/context/realtime-cart-context"
import { AuthProvider } from "@/context/unified-auth-context"
import { LocationProvider } from "@/context/location-context"
import { DeliveryModeProvider } from "@/context/delivery-mode-context"
import { ThemeWrapper } from "@/components/theme-wrapper"
import { Toaster } from "@/components/toaster"
import { SupabaseProvider } from "@/components/providers/supabase-provider"
import AuthMonitor from "@/components/auth-monitor"
import AuthSynchronizer from "@/components/auth/auth-synchronizer"
import HeaderWrapper from "@/components/header-wrapper"
import GlobalErrorHandler from "@/components/error-boundary/global-error-handler"

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap"
})

export const metadata: Metadata = {
  title: "Loop - Delivery and Services in Jersey, Channel Islands",
  description: "Need it? Loop it. Food, shopping, pharmacy, lifts, and more.",
  generator: 'v0.dev'
}

export const viewport: Viewport = {
  themeColor: "#059669",
  width: "device-width",
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="/wheel-logo.svg" sizes="any" />
        <link rel="icon" href="/wheel-logo.svg" type="image/svg+xml" />
        <link rel="apple-touch-icon" href="/wheel-logo.svg" />
        <link rel="manifest" href="/site.webmanifest?v=2" />

        {/* Add preconnect for Supabase to improve performance */}
        <link rel="preconnect" href="https://xvnxvxqvvvxvvvvv.supabase.co" />
      </head>
      <body className={`${inter.variable} font-sans`}>
        <ThemeWrapper>
          {/* Wrap everything with SupabaseProvider */}
          <SupabaseProvider>
            {/* Use unified AuthProvider for authentication */}
            <AuthProvider>
              <LocationProvider>
                <RealtimeCartProvider>
                  <DeliveryModeProvider>
                    <HeaderWrapper>
                      {children}
                    </HeaderWrapper>
                    <Toaster />
                    <AuthMonitor />
                    <AuthSynchronizer />
                    <GlobalErrorHandler />
                  </DeliveryModeProvider>
                </RealtimeCartProvider>
              </LocationProvider>
            </AuthProvider>
          </SupabaseProvider>
        </ThemeWrapper>
      </body>
    </html>
  )
}
