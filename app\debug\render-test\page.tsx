'use client';

import { useAuth } from '@/context/unified-auth-context';
import { useEffect, useRef, useState } from 'react';

export default function RenderTestPage() {
  const { user, isLoading, userProfile } = useAuth();
  const renderCount = useRef(0);
  const [renderHistory, setRenderHistory] = useState<string[]>([]);
  
  // Track renders
  renderCount.current += 1;
  const timestamp = new Date().toLocaleTimeString();
  
  useEffect(() => {
    const renderInfo = `Render #${renderCount.current} at ${timestamp} - User: ${user?.email || 'None'}, Loading: ${isLoading}`;
    console.log('🔄 RenderTest:', renderInfo);
    
    setRenderHistory(prev => {
      const newHistory = [renderInfo, ...prev.slice(0, 19)]; // Keep last 20 renders
      return newHistory;
    });
  }, [user, isLoading, userProfile, timestamp]);

  return (
    <div className="container mx-auto p-6">
      <h1 className="text-2xl font-bold mb-4">Render Test Page</h1>
      
      <div className="bg-blue-50 p-4 rounded-lg mb-6">
        <h2 className="text-lg font-semibold mb-2">Current State</h2>
        <p><strong>Render Count:</strong> {renderCount.current}</p>
        <p><strong>User:</strong> {user?.email || 'Not logged in'}</p>
        <p><strong>Loading:</strong> {isLoading ? 'Yes' : 'No'}</p>
        <p><strong>Profile:</strong> {userProfile?.name || 'None'}</p>
        <p><strong>Current Time:</strong> {timestamp}</p>
      </div>

      <div className="bg-yellow-50 p-4 rounded-lg">
        <h2 className="text-lg font-semibold mb-2">Render History (Last 20)</h2>
        <div className="max-h-96 overflow-y-auto">
          {renderHistory.map((render, index) => (
            <div key={index} className="text-sm font-mono mb-1 p-1 bg-white rounded">
              {render}
            </div>
          ))}
        </div>
      </div>

      <div className="mt-6 text-sm text-gray-600">
        <p><strong>Instructions:</strong></p>
        <ul className="list-disc list-inside">
          <li>Watch the render count - it should NOT increase every second</li>
          <li>If the count increases rapidly (every 1-2 seconds), there's still a re-render issue</li>
          <li>Normal re-renders should only happen when you login/logout or navigate</li>
        </ul>
      </div>
    </div>
  );
}
