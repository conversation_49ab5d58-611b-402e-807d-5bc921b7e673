"use client"

import { useState, useEffect, memo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { Tooltip, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { Tags, HelpCircle, CheckCircle, AlertCircle, Star } from "lucide-react"
import type { BusinessSettingsFormData } from "@/hooks/use-business-settings-form"

interface CategoriesTabProps {
  formData: BusinessSettingsFormData
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
  setFormData: (data: BusinessSettingsFormData | ((prev: BusinessSettingsFormData) => BusinessSettingsFormData)) => void
}

// Define business attributes
const BUSINESS_ATTRIBUTES = [
  { id: "wheelchair_accessible", name: "Wheelchair Accessible" },
  { id: "outdoor_seating", name: "Outdoor Seating" },
  { id: "wifi", name: "Free WiFi" },
  { id: "parking", name: "Parking Available" },
  { id: "card_payments", name: "Card Payments" },
  { id: "cash_only", name: "Cash Only" },
  { id: "family_friendly", name: "Family Friendly" },
  { id: "dog_friendly", name: "Dog Friendly" },
  { id: "takeaway", name: "Takeaway Available" },
  { id: "delivery", name: "Delivery Available" },
  { id: "vegan_options", name: "Vegan Options" },
  { id: "vegetarian_options", name: "Vegetarian Options" },
  { id: "gluten_free", name: "Gluten Free Options" },
  { id: "halal", name: "Halal" },
  { id: "kosher", name: "Kosher" },
  { id: "organic", name: "Organic" },
  { id: "local_produce", name: "Local Produce" },
  { id: "licensed", name: "Licensed (Alcohol)" },
  { id: "byob", name: "BYOB (Bring Your Own Bottle)" }
]

export function CategoriesTab({
  formData,
  handleChange,
  setFormData
}: CategoriesTabProps) {
  const [categories, setCategories] = useState<any[]>([])
  const [selectedCategories, setSelectedCategories] = useState<number[]>([])

  // Fetch available categories
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories')
        if (response.ok) {
          const data = await response.json()
          setCategories(data.categories || [])
        }
      } catch (error) {
        console.error('Error fetching categories:', error)
      }
    }
    fetchCategories()
  }, [])

  const handleAttributeChange = (attributeId: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      attributes: checked 
        ? [...prev.attributes, attributeId]
        : prev.attributes.filter(attr => attr !== attributeId)
    }))
  }

  const isCategoriesComplete = formData.attributes.length > 0

  return (
    <div className="space-y-8">
      {/* Business Attributes Section */}
      <Card className="border border-gray-200">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gray-800 rounded-lg p-2">
                <Tags className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Business Attributes</CardTitle>
                <CardDescription className="text-gray-600 text-sm">
                  Select attributes that describe your business to help customers find you
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {isCategoriesComplete ? (
                <div className="flex items-center space-x-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-xs font-medium">Complete</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1 text-amber-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-xs font-medium">Incomplete</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {BUSINESS_ATTRIBUTES.map((attribute) => (
              <div key={attribute.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
                <Checkbox
                  id={attribute.id}
                  checked={formData.attributes.includes(attribute.id)}
                  onCheckedChange={(checked) => handleAttributeChange(attribute.id, checked as boolean)}
                />
                <Label
                  htmlFor={attribute.id}
                  className="text-sm font-medium text-gray-700 cursor-pointer flex-1"
                >
                  {attribute.name}
                </Label>
              </div>
            ))}
          </div>

          {formData.attributes.length > 0 && (
            <div className="mt-6 p-4 bg-emerald-50 border border-emerald-200 rounded-lg">
              <h4 className="text-sm font-medium text-gray-700 mb-3">Selected Attributes:</h4>
              <div className="flex flex-wrap gap-2">
                {formData.attributes.map((attrId) => {
                  const attribute = BUSINESS_ATTRIBUTES.find(attr => attr.id === attrId)
                  return attribute ? (
                    <Badge key={attrId} variant="secondary" className="bg-emerald-100 text-emerald-800">
                      {attribute.name}
                    </Badge>
                  ) : null
                })}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Food Safety & Compliance Section */}
      <Card className="border border-gray-200">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-gray-800 rounded-lg p-2">
              <Star className="h-5 w-5 text-white" />
            </div>
            <div>
              <CardTitle className="text-lg font-semibold text-gray-900">Food Safety & Compliance</CardTitle>
              <CardDescription className="text-gray-600 text-sm">
                Food safety ratings and allergen information for customer confidence
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="hygiene_rating" className="text-sm font-medium text-gray-700">
                  Food Hygiene Rating
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">Your official food hygiene rating from Jersey's Environmental Health department (0-5 stars).</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Input
                id="hygiene_rating"
                name="hygiene_rating"
                value={formData.hygiene_rating}
                onChange={handleChange}
                placeholder="e.g. 5 stars, 4 stars, etc."
                className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>
          </div>

          <div className="mt-6 space-y-2">
            <div className="flex items-center space-x-2">
              <Label htmlFor="allergens_info" className="text-sm font-medium text-gray-700">
                Allergen Information
              </Label>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">Important allergen information for customers with dietary restrictions. Include common allergens present in your food.</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <Textarea
              id="allergens_info"
              name="allergens_info"
              value={formData.allergens_info}
              onChange={handleChange}
              placeholder="e.g. Contains nuts, gluten, dairy. Please inform staff of any allergies..."
              rows={4}
              className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
