"use client"

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  TabsList,
  TabsTrigger,
} from "@/components/ui/tabs"
import {
  MessageSquare,
  Package,
  Star,
  AlertCircle,
  Utensils,
  Users,
  MapPin,
  TrendingUp,
  Clock,
  Search,
  Filter,
  Globe,
  Lock,
  Zap,
  Heart,
  Truck,
  Building2,
  ArrowRight,
  MessageCircle,
  Eye,
  EyeOff,
  ChevronRight,
  Plus,
  Loader2,
  Settings,
  Reply,
  Bell,
  Paperclip,
  Image,
  FileText,
  Video,
  Music
} from "lucide-react"
import { cn } from "@/lib/utils"
import { MessageTemplateComposer } from './MessageTemplateComposer'
import { MESSAGE_TEMPLATES, MessageTemplate } from '@/types/message-templates'
import { useAuth } from '@/context/unified-auth-context'
import { CommunityDiscovery } from './CommunityDiscovery'
import { ReviewBasedMessages } from './ReviewBasedMessages'
import { UserDiscovery } from './UserDiscovery'
import { RealTimeChat } from './RealTimeChat'
import { MessageThreads } from './MessageThreads'
import { NotificationCenter } from './NotificationCenter'
// import { PopularSearchesFixed } from './PopularSearchesFixed'

interface EnhancedQuickAction {
  id: string
  type: string
  title: string
  description: string
  icon: string
  color: string
  category: string
  categoryDisplayName: string
  urgencyLevel: string
  urgent: boolean
  context?: any
  actionData: any
}

interface UserCapabilities {
  canPlaceOrders: boolean
  canManageBusiness: boolean
  canDrive: boolean
  primaryRole: string
  managedBusinesses: string[]
  approvedBusinesses: string[]
}

interface MessageContext {
  hasActiveOrders: boolean
  hasRecentCompletedOrders: boolean
  activeOrderCount: number
  recentOrderCount: number
  lastBusinessName?: string
}

export function EnhancedMessagesPage() {
  const { user } = useAuth()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedSection, setSelectedSection] = useState<'quick' | 'community' | 'conversations'>('quick')
  const [quickActions, setQuickActions] = useState<EnhancedQuickAction[]>([])
  const [userCapabilities, setUserCapabilities] = useState<UserCapabilities | null>(null)
  const [messageContext, setMessageContext] = useState<MessageContext | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Template composer state
  const [selectedTemplate, setSelectedTemplate] = useState<MessageTemplate | null>(null)
  const [showTemplateComposer, setShowTemplateComposer] = useState(false)

  useEffect(() => {
    if (user?.id) {
      fetchQuickActions()
    }
  }, [user?.id])

  const fetchQuickActions = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/messages/enhanced-quick-actions?auth_id=${user?.id}`)

      if (!response.ok) {
        throw new Error('Failed to fetch quick actions')
      }

      const data = await response.json()

      if (data.success) {
        setQuickActions(data.quick_actions)
        setUserCapabilities(data.user_capabilities)
        setMessageContext(data.context)
      } else {
        throw new Error(data.error || 'Unknown error')
      }
    } catch (err: any) {
      console.error('Error fetching quick actions:', err)
      setError(err.message)
    } finally {
      setLoading(false)
    }
  }

  const handleQuickAction = (action: EnhancedQuickAction) => {
    console.log('Quick action clicked:', action)

    // Find the corresponding template from MESSAGE_TEMPLATES
    const template = MESSAGE_TEMPLATES.find(t => t.id === action.id || t.title === action.title)

    if (template) {
      console.log('📧 Opening template composer for:', template.title)
      setSelectedTemplate(template)
      setShowTemplateComposer(true)
    } else {
      console.log('⚠️ No template found for action:', action.title)
      // For non-template actions, you could handle them differently
      // For now, let's create a basic template from the action
      const basicTemplate: MessageTemplate = {
        id: action.id,
        category: 'general',
        title: action.title,
        content: action.description || 'Please provide more details about your request.',
        user_roles: ['customer', 'business', 'driver'],
        priority: 1
      }
      setSelectedTemplate(basicTemplate)
      setShowTemplateComposer(true)
    }
  }

  const handleTemplateSend = async (content: string, template: MessageTemplate, privacySettings?: any) => {
    try {
      console.log('Sending template message:', { content, template, privacySettings })

      // TODO: Implement actual message sending
      // For now, just close the composer
      setShowTemplateComposer(false)
      setSelectedTemplate(null)

      // You could show a success message here
      console.log('✅ Message sent successfully!')

    } catch (error) {
      console.error('Error sending template message:', error)
    }
  }

  const handleTemplateCancel = () => {
    setShowTemplateComposer(false)
    setSelectedTemplate(null)
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="flex items-center gap-2">
          <Loader2 className="h-6 w-6 animate-spin text-emerald-600" />
          <span className="text-gray-600">Loading messaging system...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="max-w-md">
          <CardContent className="p-6 text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Messages</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={fetchQuickActions} variant="outline">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-4xl mx-auto px-4 py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
              <p className="text-sm text-gray-600">Connect with the Loop Jersey community</p>
            </div>
            <div className="flex items-center gap-2 sm:gap-3">
              <div className="relative flex-1 sm:flex-none">
                <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search messages..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 w-full sm:w-64"
                />
              </div>
              <Button variant="outline" size="sm" className="whitespace-nowrap">
                <Filter className="h-4 w-4 sm:mr-2" />
                <span className="hidden sm:inline">Filter</span>
              </Button>
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="flex items-center gap-1 mt-4 overflow-x-auto pb-2">
            <Button
              variant={selectedSection === 'quick' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedSection('quick')}
              className="flex items-center gap-2 whitespace-nowrap"
            >
              <Zap className="h-4 w-4" />
              <span className="hidden sm:inline">Quick Actions</span>
              <span className="sm:hidden">Quick</span>
            </Button>
            <Button
              variant={selectedSection === 'community' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedSection('community')}
              className="flex items-center gap-2 whitespace-nowrap"
            >
              <Users className="h-4 w-4" />
              <span className="hidden sm:inline">Community</span>
              <span className="sm:hidden">Community</span>
            </Button>
            <Button
              variant={selectedSection === 'discovery' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedSection('discovery')}
              className="flex items-center gap-2 whitespace-nowrap"
            >
              <Globe className="h-4 w-4" />
              <span className="hidden sm:inline">Discovery</span>
              <span className="sm:hidden">Discover</span>
            </Button>
            <Button
              variant={selectedSection === 'conversations' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedSection('conversations')}
              className="flex items-center gap-2 whitespace-nowrap"
            >
              <MessageSquare className="h-4 w-4" />
              <span className="hidden sm:inline">Your Conversations</span>
              <span className="sm:hidden">Chats</span>
              <Badge variant="secondary" className="ml-1">0</Badge>
            </Button>
            <Button
              variant={selectedSection === 'advanced' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedSection('advanced')}
              className="flex items-center gap-2 whitespace-nowrap"
            >
              <Settings className="h-4 w-4" />
              <span className="hidden sm:inline">Advanced</span>
              <span className="sm:hidden">More</span>
            </Button>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 py-6">
        {/* User Capabilities Summary */}
        {userCapabilities && (
          <Card className="mb-6">
            <CardContent className="p-4">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-700">Your capabilities:</span>
                  {userCapabilities.canPlaceOrders && (
                    <Badge variant="outline" className="text-xs">Customer</Badge>
                  )}
                  {userCapabilities.canManageBusiness && (
                    <Badge variant="outline" className="text-xs bg-green-50 text-green-700">
                      Business Manager
                    </Badge>
                  )}
                  {userCapabilities.canDrive && (
                    <Badge variant="outline" className="text-xs bg-orange-50 text-orange-700">
                      Driver
                    </Badge>
                  )}
                </div>
                {messageContext && (
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    {messageContext.hasActiveOrders && (
                      <span className="flex items-center gap-1">
                        <Package className="h-3 w-3" />
                        {messageContext.activeOrderCount} active order{messageContext.activeOrderCount !== 1 ? 's' : ''}
                      </span>
                    )}
                    {messageContext.hasRecentCompletedOrders && (
                      <span className="flex items-center gap-1">
                        <Star className="h-3 w-3" />
                        Recent orders to review
                      </span>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions Section */}
        {selectedSection === 'quick' && (
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Zap className="h-5 w-5 text-emerald-600" />
                <h2 className="text-lg font-semibold text-gray-900">Fast & Functional</h2>
                <Badge variant="outline" className="text-xs">Get quick help</Badge>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {quickActions.map((action) => (
                  <Card
                    key={action.id}
                    className={cn(
                      "cursor-pointer hover:shadow-md transition-all duration-200 border-2 hover:border-emerald-200",
                      action.urgent && "ring-2 ring-orange-200 bg-orange-50"
                    )}
                    onClick={() => handleQuickAction(action)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start gap-3">
                        <div className={cn("p-2 rounded-lg text-white", action.color)}>
                          <span className="text-lg">{action.icon}</span>
                        </div>
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-medium text-gray-900">{action.title}</h3>
                            {action.urgent && (
                              <Badge variant="destructive" className="text-xs">Urgent</Badge>
                            )}
                            <Badge variant="outline" className="text-xs opacity-60">
                              {action.categoryDisplayName}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{action.description}</p>
                          {action.context && (
                            <div className="text-xs text-emerald-600 flex items-center gap-1">
                              <Clock className="h-3 w-3" />
                              {action.context.lastBusinessName && `Last order: ${action.context.lastBusinessName}`}
                              {action.context.activeOrderCount > 0 && `${action.context.activeOrderCount} active orders`}
                              {action.context.managedBusinesses?.length > 0 && `Managing: ${action.context.managedBusinesses.join(', ')}`}
                            </div>
                          )}
                        </div>
                        <ArrowRight className="h-4 w-4 text-gray-400" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>

            {/* Empty State for No Actions */}
            {quickActions.length === 0 && (
              <Card className="border-dashed border-2 border-gray-200">
                <CardContent className="p-8 text-center">
                  <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Quick Actions Available</h3>
                  <p className="text-gray-500 mb-4">
                    Quick actions will appear here based on your activity and capabilities
                  </p>
                  <Button variant="outline" size="sm">
                    Refresh
                  </Button>
                </CardContent>
              </Card>
            )}
          </div>
        )}

        {/* Community Section */}
        {selectedSection === 'community' && (
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Users className="h-5 w-5 text-blue-600" />
                <h2 className="text-lg font-semibold text-gray-900">Community & Discovery</h2>
              </div>

              <CommunityDiscovery
                user={user}
                onJoinConversation={(messageId) => {
                  console.log('Joining community conversation:', messageId)
                  // TODO: Navigate to conversation
                }}
                onStartNewPublicMessage={() => {
                  console.log('Starting new public message')
                  // TODO: Open public message composer
                }}
              />

              <ReviewBasedMessages
                user={user}
                onMessageSelect={(message) => {
                  console.log('Review message selected:', message)
                  // TODO: Navigate to message thread
                }}
              />
            </div>
          </div>
        )}

        {/* Discovery Section */}
        {selectedSection === 'discovery' && (
          <div className="space-y-6">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Globe className="h-5 w-5 text-blue-600" />
                <h2 className="text-lg font-semibold text-gray-900">Community Discovery</h2>
              </div>

              {/* Discovery Layout */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Main Discovery Content */}
                <div className="lg:col-span-2">
                  <UserDiscovery
                    onUserSelect={(user) => {
                      console.log('User selected for connection:', user)
                      // TODO: Navigate to user profile or connection flow
                    }}
                    onMessageUser={(user) => {
                      console.log('Starting message with user:', user)
                      // TODO: Open message composer with user
                    }}
                  />
                </div>

                {/* Discovery Sidebar */}
                <div className="lg:col-span-1 space-y-6">
                  {/* Popular Searches */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Popular Searches</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-sm text-gray-500 text-center py-4">
                        Popular searches coming soon
                      </p>
                    </CardContent>
                  </Card>

                  {/* Discovery Stats */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Discovery Stats</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-3">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Active Members</span>
                        <span className="font-medium">247</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Businesses</span>
                        <span className="font-medium">89</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Drivers</span>
                        <span className="font-medium">34</span>
                      </div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Parishes Covered</span>
                        <span className="font-medium">12</span>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Quick Discovery Actions */}
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-sm">Quick Discovery</CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-2">
                      <Button variant="outline" size="sm" className="w-full justify-start text-xs">
                        <Users className="h-3 w-3 mr-2" />
                        Find Local Experts
                      </Button>
                      <Button variant="outline" size="sm" className="w-full justify-start text-xs">
                        <Building2 className="h-3 w-3 mr-2" />
                        Discover Businesses
                      </Button>
                      <Button variant="outline" size="sm" className="w-full justify-start text-xs">
                        <Truck className="h-3 w-3 mr-2" />
                        Connect with Drivers
                      </Button>
                      <Button variant="outline" size="sm" className="w-full justify-start text-xs">
                        <MapPin className="h-3 w-3 mr-2" />
                        Explore by Parish
                      </Button>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Advanced Section */}
        {selectedSection === 'advanced' && (
          <div className="space-y-6">
            {/* Header */}
            <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-6 border border-purple-100">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Settings className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <h2 className="text-xl font-bold text-gray-900">Advanced Messaging Features</h2>
                    <p className="text-sm text-gray-600">Real-time communication, threading, and notifications</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Advanced Features Tabs */}
            <Tabs defaultValue="realtime" className="space-y-6">
              <TabsList className="grid w-full grid-cols-4 h-12 bg-gray-50 p-1">
                <TabsTrigger
                  value="realtime"
                  className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <MessageSquare className="h-4 w-4" />
                  <span className="hidden sm:inline">Real-Time Chat</span>
                  <span className="sm:hidden">Chat</span>
                </TabsTrigger>
                <TabsTrigger
                  value="threads"
                  className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <Reply className="h-4 w-4" />
                  <span className="hidden sm:inline">Message Threads</span>
                  <span className="sm:hidden">Threads</span>
                </TabsTrigger>
                <TabsTrigger
                  value="notifications"
                  className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <Bell className="h-4 w-4" />
                  <span className="hidden sm:inline">Notifications</span>
                  <span className="sm:hidden">Alerts</span>
                </TabsTrigger>
                <TabsTrigger
                  value="attachments"
                  className="flex items-center gap-2 data-[state=active]:bg-white data-[state=active]:shadow-sm"
                >
                  <Paperclip className="h-4 w-4" />
                  <span className="hidden sm:inline">Attachments</span>
                  <span className="sm:hidden">Files</span>
                </TabsTrigger>
              </TabsList>

              <TabsContent value="realtime" className="space-y-0">
                <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
                  {/* Main Chat Area */}
                  <div className="xl:col-span-3">
                    <RealTimeChat
                      conversationId="general"
                      currentUserId={user?.id || 'demo-user'}
                      currentUserName={user?.email || 'Demo User'}
                    />
                  </div>

                  {/* Sidebar */}
                  <div className="xl:col-span-1 space-y-4">
                    {/* Real-Time Features */}
                    <Card className="border-green-200 bg-green-50">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm flex items-center gap-2">
                          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                          Real-Time Features
                        </CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3 pt-0">
                        <div className="flex items-center gap-3">
                          <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse" />
                          <span className="text-sm text-green-800">Live messaging</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="h-2 w-2 bg-blue-500 rounded-full" />
                          <span className="text-sm text-blue-800">Typing indicators</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="h-2 w-2 bg-purple-500 rounded-full" />
                          <span className="text-sm text-purple-800">Online presence</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="h-2 w-2 bg-orange-500 rounded-full" />
                          <span className="text-sm text-orange-800">Message reactions</span>
                        </div>
                      </CardContent>
                    </Card>

                    {/* Quick Stats */}
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">Chat Statistics</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3 pt-0">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Messages today</span>
                          <span className="font-medium">24</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Active users</span>
                          <span className="font-medium">3</span>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Reactions sent</span>
                          <span className="font-medium">12</span>
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="threads" className="space-y-0">
                <MessageThreads
                  onThreadSelect={(thread) => {
                    console.log('Thread selected:', thread)
                    // TODO: Navigate to thread view
                  }}
                />
              </TabsContent>

              <TabsContent value="notifications" className="space-y-0">
                <NotificationCenter
                  currentUserId={user?.id || 'demo-user'}
                />
              </TabsContent>

              <TabsContent value="attachments" className="space-y-0">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                  {/* Main Content */}
                  <div className="lg:col-span-2">
                    <Card>
                      <CardHeader>
                        <CardTitle className="flex items-center gap-2">
                          <Paperclip className="h-5 w-5 text-blue-600" />
                          File Attachments & Media
                        </CardTitle>
                      </CardHeader>
                      <CardContent>
                        <div className="text-center py-12">
                          <div className="p-4 bg-blue-50 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                            <Paperclip className="h-10 w-10 text-blue-600" />
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900 mb-2">Share Files & Media</h3>
                          <p className="text-gray-600 mb-6 max-w-md mx-auto">
                            Upload and share images, documents, videos, and audio files in your conversations
                          </p>

                          {/* File Type Grid */}
                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-lg mx-auto">
                            <div className="p-4 border-2 border-dashed border-blue-200 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors cursor-pointer">
                              <Image className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                              <span className="text-sm font-medium text-blue-800">Images</span>
                              <p className="text-xs text-blue-600 mt-1">JPG, PNG, GIF</p>
                            </div>
                            <div className="p-4 border-2 border-dashed border-green-200 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors cursor-pointer">
                              <FileText className="h-8 w-8 text-green-600 mx-auto mb-2" />
                              <span className="text-sm font-medium text-green-800">Documents</span>
                              <p className="text-xs text-green-600 mt-1">PDF, DOC, TXT</p>
                            </div>
                            <div className="p-4 border-2 border-dashed border-purple-200 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors cursor-pointer">
                              <Video className="h-8 w-8 text-purple-600 mx-auto mb-2" />
                              <span className="text-sm font-medium text-purple-800">Videos</span>
                              <p className="text-xs text-purple-600 mt-1">MP4, AVI, MOV</p>
                            </div>
                            <div className="p-4 border-2 border-dashed border-orange-200 rounded-lg hover:border-orange-400 hover:bg-orange-50 transition-colors cursor-pointer">
                              <Music className="h-8 w-8 text-orange-600 mx-auto mb-2" />
                              <span className="text-sm font-medium text-orange-800">Audio</span>
                              <p className="text-xs text-orange-600 mt-1">MP3, WAV, M4A</p>
                            </div>
                          </div>

                          <Button className="mt-6" size="lg">
                            <Plus className="h-4 w-4 mr-2" />
                            Upload Files
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  </div>

                  {/* Sidebar */}
                  <div className="lg:col-span-1 space-y-4">
                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">Upload Limits</CardTitle>
                      </CardHeader>
                      <CardContent className="space-y-3 pt-0">
                        <div className="text-sm">
                          <div className="flex justify-between mb-1">
                            <span className="text-gray-600">Max file size</span>
                            <span className="font-medium">10 MB</span>
                          </div>
                          <div className="flex justify-between mb-1">
                            <span className="text-gray-600">Files per message</span>
                            <span className="font-medium">5</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Storage used</span>
                            <span className="font-medium">2.3 GB</span>
                          </div>
                        </div>
                      </CardContent>
                    </Card>

                    <Card>
                      <CardHeader className="pb-3">
                        <CardTitle className="text-sm">Recent Files</CardTitle>
                      </CardHeader>
                      <CardContent className="pt-0">
                        <p className="text-sm text-gray-500 text-center py-4">
                          No recent files
                        </p>
                      </CardContent>
                    </Card>
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          </div>
        )}

        {/* Conversations Section */}
        {selectedSection === 'conversations' && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-emerald-600" />
                <h2 className="text-lg font-semibold text-gray-900">Your Conversations</h2>
              </div>
              <Button size="sm" className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                New Message
              </Button>
            </div>

            {/* Empty State for Conversations */}
            <Card className="border-dashed border-2 border-gray-200">
              <CardContent className="p-8 text-center">
                <MessageSquare className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Start Your First Conversation</h3>
                <p className="text-gray-500 mb-4">
                  Connect with businesses, drivers, or the community to get the most out of Loop Jersey
                </p>
                <div className="flex justify-center gap-3">
                  <Button variant="outline" size="sm">
                    Ask a Business
                  </Button>
                  <Button size="sm">
                    Join Community
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>

      {/* Template Composer Modal */}
      {showTemplateComposer && selectedTemplate && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <MessageTemplateComposer
            template={selectedTemplate}
            context={{}} // You can pass context here if needed
            onSend={handleTemplateSend}
            onCancel={handleTemplateCancel}
            className="max-w-lg w-full"
          />
        </div>
      )}
    </div>
  )
}
