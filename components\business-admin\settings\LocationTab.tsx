"use client"

import { memo } from "react"
import { Card, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Toolt<PERSON>, TooltipContent, TooltipTrigger } from "@/components/ui/tooltip"
import { MapPin, HelpCircle, CheckCircle, AlertCircle } from "lucide-react"
import type { BusinessSettingsFormData } from "@/hooks/use-business-settings-form"

interface LocationTabProps {
  formData: BusinessSettingsFormData
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void
}

export const LocationTab = memo(function LocationTab({
  formData,
  handleChange
}: LocationTabProps) {
  const isLocationComplete = formData.address && formData.postcode

  return (
    <div className="space-y-8">
      {/* Location Information Section */}
      <Card className="border border-gray-200">
        <CardHeader className="bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="bg-gray-800 rounded-lg p-2">
                <MapPin className="h-5 w-5 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg font-semibold text-gray-900">Location & Address</CardTitle>
                <CardDescription className="text-gray-600 text-sm">
                  Your business location helps customers find you and enables delivery services
                </CardDescription>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              {isLocationComplete ? (
                <div className="flex items-center space-x-1 text-green-600">
                  <CheckCircle className="h-4 w-4" />
                  <span className="text-xs font-medium">Complete</span>
                </div>
              ) : (
                <div className="flex items-center space-x-1 text-amber-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-xs font-medium">Incomplete</span>
                </div>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="address" className="text-sm font-medium text-gray-700 flex items-center">
                  Street Address
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">Enter your full street address including building number and street name. This helps customers find your location.</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Input
                id="address"
                name="address"
                value={formData.address}
                onChange={handleChange}
                placeholder="e.g. 123 High Street, St. Helier"
                className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Label htmlFor="postcode" className="text-sm font-medium text-gray-700 flex items-center">
                  Postcode
                  <span className="text-red-500 ml-1">*</span>
                </Label>
                <Tooltip>
                  <TooltipTrigger>
                    <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">Jersey postcode helps with delivery routing and location services.</p>
                  </TooltipContent>
                </Tooltip>
              </div>
              <Input
                id="postcode"
                name="postcode"
                value={formData.postcode}
                onChange={handleChange}
                placeholder="e.g. JE2 3AB"
                className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
              />
            </div>
          </div>

          <div className="mt-6 space-y-2">
            <div className="flex items-center space-x-2">
              <Label htmlFor="location" className="text-sm font-medium text-gray-700">
                Parish
              </Label>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">The Jersey parish where your business is located (e.g. St Helier, St Brelade, St Saviour).</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <Input
              id="location"
              name="location"
              value={formData.location}
              onChange={handleChange}
              placeholder="e.g. St Helier"
              className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
            />
          </div>

          <div className="mt-6 space-y-2">
            <div className="flex items-center space-x-2">
              <Label htmlFor="coordinates" className="text-sm font-medium text-gray-700">
                GPS Coordinates
              </Label>
              <Tooltip>
                <TooltipTrigger>
                  <HelpCircle className="h-4 w-4 text-gray-400 hover:text-gray-600" />
                </TooltipTrigger>
                <TooltipContent>
                  <p className="max-w-xs">Optional: Enter latitude,longitude coordinates for precise location mapping. Format: 49.1858,-2.1054<br/><br/>To get coordinates from Google Maps: Search for your business, right-click on the location pin, and select the coordinates that appear at the top of the menu.</p>
                </TooltipContent>
              </Tooltip>
            </div>
            <Input
              id="coordinates"
              name="coordinates"
              value={formData.coordinates}
              onChange={handleChange}
              placeholder="e.g. 49.1858,-2.1054"
              className="border-gray-300 focus:border-emerald-500 focus:ring-emerald-500"
            />
          </div>
        </CardContent>
      </Card>
    </div>
  )
})
