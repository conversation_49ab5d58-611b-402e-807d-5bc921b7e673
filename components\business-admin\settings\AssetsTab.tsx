"use client"

import { memo } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Info } from "lucide-react"
import FileUpload from "@/components/ui/file-upload"
import type { BusinessSettingsFormData } from "@/hooks/use-business-settings-form"

interface AssetsTabProps {
  formData: BusinessSettingsFormData
  setFormData: (data: BusinessSettingsFormData | ((prev: BusinessSettingsFormData) => BusinessSettingsFormData)) => void
  businessSlug?: string
}

export const AssetsTab = memo(function AssetsTab({ formData, setFormData, businessSlug }: AssetsTabProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Business Logo</CardTitle>
        <CardDescription>
          Upload and manage your business logo
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Logo</h3>
          <p className="text-sm text-gray-500">
            Your logo will be displayed in search results and throughout the platform.
            A square image with transparent background is recommended.
          </p>

          {/* Logo Creation Recommendations */}
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Logo Creation Tips</h4>
            <ul className="text-sm text-blue-800 space-y-1">
              <li>• For AI-generated logos, try <a href="https://ideogram.ai" target="_blank" rel="noopener noreferrer" className="underline hover:text-blue-600">ideogram.ai</a> - excellent for creating professional business logos</li>
              <li>• Use square format (1:1 ratio) for best results</li>
              <li>• Transparent background works best for versatility</li>
              <li>• Keep it simple and readable at small sizes</li>
              <li>• High resolution (minimum 500x500px) recommended</li>
            </ul>
          </div>

          {businessSlug && (
            <FileUpload
              type="logo"
              businessId={businessSlug}
              currentImageUrl={formData.logo_url}
              onUploadComplete={(url) => {
                setFormData(prev => ({ ...prev, logo_url: url }))
              }}
            />
          )}
        </div>

        <Alert className="bg-green-50 border-green-200 text-green-800">
          <Info className="h-4 w-4" />
          <AlertDescription>
            Your logo will be saved when you click the "Save Changes" button.
          </AlertDescription>
        </Alert>
      </CardContent>
    </Card>
  )
})
