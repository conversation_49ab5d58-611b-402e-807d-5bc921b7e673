import type { Restaurant, Shop } from '@/types/business';
// Removed import of deleted business-delivery-utils

// Get Supabase URL and keys from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Helper function to make API calls to Supabase
async function fetchFromSupabase(path: string, params: Record<string, string> = {}) {
  try {
    // Log the request for debugging
    console.log(`Fetching from ${path} with params:`, params);

    // Build query string manually to handle special Supabase query parameters
    let queryParts: string[] = [];

    for (const [key, value] of Object.entries(params)) {
      // Handle special cases for Supabase
      if (key === 'select') {
        queryParts.push(`select=${encodeURIComponent(value)}`);
      } else if (key === 'order') {
        queryParts.push(`order=${encodeURIComponent(value)}`);
      } else if (key === 'in') {
        // For 'in' operator, we need to handle it specially
        const [column, values] = value.split(':');
        queryParts.push(`${encodeURIComponent(column)}=in.(${encodeURIComponent(values)})`);
      } else if (key === 'eq') {
        // For 'eq' operator, we need to handle it specially
        const [column, val] = value.split(':');
        queryParts.push(`${encodeURIComponent(column)}=eq.${encodeURIComponent(val)}`);
      } else {
        // For other parameters, use the value as is
        queryParts.push(`${encodeURIComponent(key)}=${encodeURIComponent(value)}`);
      }
    }

    // Construct the final query string
    const queryString = queryParts.length > 0 ? '?' + queryParts.join('&') : '';

    // Construct the full URL
    const url = `${supabaseUrl}/rest/v1/${path}${queryString}`;
    console.log('Request URL:', url);

    // Make API call
    const response = await fetch(url, {
      headers: {
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${supabaseAnonKey}`,
        'Content-Type': 'application/json',
        'Accept': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API call failed: ${response.status} ${response.statusText}`, errorText);
      throw new Error(`API call failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error in fetchFromSupabase:', error);
    throw error;
  }
}

// Get business by ID (slug) - works for all business types
export async function getBusinessById(slug: string): Promise<any | null> {
  try {
    // Fetch business
    const businesses = await fetchFromSupabase('businesses', {
      'select': '*',
      'eq': `slug:${slug}`
    });

    if (!businesses || businesses.length === 0) {
      console.error(`Business ${slug} not found`);
      return null;
    }

    const business = businesses[0];

    // Get business type information
    const businessTypes = await fetchFromSupabase('business_types', {
      'select': '*',
      'eq': `id:${business.business_type_id}`
    });

    if (!businessTypes || businessTypes.length === 0) {
      console.error('Business type not found');
      return null;
    }

    const businessType = businessTypes[0];
    console.log(`Business ${slug} is of type: ${businessType.name} (${businessType.slug})`);

    // Get attributes based on business type
    let attributes: string[] = [];
    try {
      // We need to handle multiple eq parameters differently
      const attributesUrl = `business_attributes?select=attribute_type,attribute_value&business_id=eq.${business.id}`;
      const attributesResponse = await fetch(`${supabaseUrl}/rest/v1/${attributesUrl}`, {
        headers: {
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }).then(res => {
        if (!res.ok) {
          throw new Error(`API call failed: ${res.status} ${res.statusText}`);
        }
        return res.json();
      });

      if (attributesResponse && attributesResponse.length > 0) {
        attributes = attributesResponse.map((attr: any) => attr.attribute_value);
      }
    } catch (error) {
      console.error(`Error fetching attributes for business ${slug}:`, error);

      // Set default attributes based on business type
      switch (businessType.slug) {
        case 'restaurant':
          attributes = ['Italian', 'Casual Dining'];
          break;
        case 'shop':
          attributes = ['Grocery', 'Convenience'];
          break;
        case 'pharmacy':
          attributes = ['Prescription', 'Over-the-counter'];
          break;
        case 'cafe':
          attributes = ['Coffee', 'Bakery'];
          break;
        case 'errand':
          attributes = ['General'];
          break;
        default:
          attributes = ['General'];
      }
    }

    // Calculate delivery time
    // Allow for 0 preparation time if explicitly set
    const prepTime = business.preparation_time_minutes !== undefined && business.preparation_time_minutes !== null
      ? business.preparation_time_minutes
      : 15;

    // Base delivery time is prep time + estimated travel time
    const estimatedTravelTime = 10; // Default estimated travel time
    const baseTime = prepTime + estimatedTravelTime;

    // Create a consistent time range format (same as used in business cards)
    const timeRange = `${Math.max(5, baseTime - 5)}-${baseTime + 5} min`;

    // Fetch menu categories and items
    let menuCategories = [];

    try {
      // Get all custom categories for this business
      const customCategoriesUrl = `business_custom_categories?select=id,name,slug,description,display_order,level,parent_category&business_id=eq.${business.id}&is_active=eq.true&order=display_order.asc`;
      const customCategories = await fetch(`${supabaseUrl}/rest/v1/${customCategoriesUrl}`, {
        headers: {
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      }).then(res => {
        if (!res.ok) {
          throw new Error(`API call failed: ${res.status} ${res.statusText}`);
        }
        return res.json();
      });

      if (customCategories && customCategories.length > 0) {
        // For each custom category, fetch menu items
        menuCategories = await Promise.all(
          customCategories.map(async (category: any) => {
            // Fetch products for this custom category with variants and customizations
            const productsUrl = `products?select=id,name,description,price,image_url,is_featured,is_available&custom_category_id=eq.${category.id}&business_id=eq.${business.id}`;
            const products = await fetch(`${supabaseUrl}/rest/v1/${productsUrl}`, {
              headers: {
                'apikey': supabaseAnonKey,
                'Authorization': `Bearer ${supabaseAnonKey}`,
                'Content-Type': 'application/json',
                'Accept': 'application/json'
              }
            }).then(res => {
              if (!res.ok) {
                throw new Error(`API call failed: ${res.status} ${res.statusText}`);
              }
              return res.json();
            });

            // For each product, fetch variants and customizations
            const menuItems = await Promise.all(products.map(async (product: any) => {
              // Fetch product variants
              let variants = [];
              try {
                const variantsUrl = `product_variants?select=id,name,price,is_default&product_id=eq.${product.id}&order=is_default.desc,id.asc`;
                const variantsResponse = await fetch(`${supabaseUrl}/rest/v1/${variantsUrl}`, {
                  headers: {
                    'apikey': supabaseAnonKey,
                    'Authorization': `Bearer ${supabaseAnonKey}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                  }
                });

                if (variantsResponse.ok) {
                  const variantsData = await variantsResponse.json();
                  variants = variantsData.map((variant: any) => ({
                    id: variant.id.toString(),
                    name: variant.name,
                    price: variant.price,
                    isDefault: variant.is_default || false
                  }));
                }
              } catch (error) {
                console.error(`Error fetching variants for product ${product.id}:`, error);
              }

              // Fetch customization groups and options
              let customizations = [];
              try {
                const customizationGroupsUrl = `customization_groups?select=id,name,is_required,is_multiple,min_selections,max_selections&product_id=eq.${product.id}&order=id.asc`;
                const groupsResponse = await fetch(`${supabaseUrl}/rest/v1/${customizationGroupsUrl}`, {
                  headers: {
                    'apikey': supabaseAnonKey,
                    'Authorization': `Bearer ${supabaseAnonKey}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                  }
                });

                if (groupsResponse.ok) {
                  const groupsData = await groupsResponse.json();

                  // For each group, fetch its options
                  customizations = await Promise.all(groupsData.map(async (group: any) => {
                    const optionsUrl = `customization_options?select=id,name,price,is_default&group_id=eq.${group.id}&order=display_order.asc,id.asc`;
                    const optionsResponse = await fetch(`${supabaseUrl}/rest/v1/${optionsUrl}`, {
                      headers: {
                        'apikey': supabaseAnonKey,
                        'Authorization': `Bearer ${supabaseAnonKey}`,
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                      }
                    });

                    let options = [];
                    if (optionsResponse.ok) {
                      const optionsData = await optionsResponse.json();
                      options = optionsData.map((option: any) => ({
                        id: option.id.toString(),
                        name: option.name,
                        price: option.price || 0
                      }));
                    }

                    return {
                      id: group.id.toString(),
                      name: group.name,
                      required: group.is_required || false,
                      isMultiple: group.is_multiple || false,
                      minSelections: group.min_selections || 0,
                      maxSelections: group.max_selections || 1,
                      options: options
                    };
                  }));
                }
              } catch (error) {
                console.error(`Error fetching customizations for product ${product.id}:`, error);
              }

              return {
                id: product.id.toString(),
                name: product.name,
                description: product.description || '',
                price: product.price,
                image: product.image_url,
                isPopular: product.is_featured || false,
                isAvailable: product.is_available !== false, // Default to true if not specified
                variants: variants,
                customizations: customizations
              };
            }));

            // Return the category with its menu items and all fields needed for aisle layout
            return {
              id: category.id.toString(),
              name: category.name,
              slug: category.slug,
              description: category.description,
              display_order: category.display_order,
              level: category.level,
              parent_category: category.parent_category,
              items: menuItems
            };
          })
        );
      }
    } catch (error) {
      console.error(`Error fetching custom menu categories and items for business ${slug}:`, error);
    }

    // Format delivery fee for display
    const formattedDeliveryFee = business.delivery_fee === 0
      ? "Free delivery"
      : `£${business.delivery_fee?.toFixed(2) || "2.00"}`;

    // Calculate actual rating from Reviews table
    let actualRating = 0;
    let actualReviewCount = 0;

    try {
      // Fetch reviews for this business to calculate actual rating
      const reviewsUrl = `reviews?select=rating&business_id=eq.${business.id}`;
      const reviewsResponse = await fetch(`${supabaseUrl}/rest/v1/${reviewsUrl}`, {
        headers: {
          'apikey': supabaseAnonKey,
          'Authorization': `Bearer ${supabaseAnonKey}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });

      if (reviewsResponse.ok) {
        const reviewsData = await reviewsResponse.json();
        if (reviewsData && reviewsData.length > 0) {
          const totalRating = reviewsData.reduce((sum: number, review: any) => sum + review.rating, 0);
          actualRating = Math.round((totalRating / reviewsData.length) * 10) / 10; // Round to 1 decimal place
          actualReviewCount = reviewsData.length;
        }
      }
    } catch (error) {
      console.error(`Error fetching reviews for business ${slug}:`, error);
      // Fall back to database values if review fetch fails
      actualRating = business.rating || 0;
      actualReviewCount = business.review_count || 0;
    }

    // Transform to generic business object with type-specific attributes
    const genericBusiness: any = {
      id: business.id, // Use the numeric ID from the database
      slug: business.slug, // Keep the slug for routing
      name: business.name,
      coverImage: business.banner_url || '/placeholder.svg',
      image: business.logo_url || business.banner_url || '/placeholder.svg',
      logo_url: business.logo_url, // Preserve the original logo_url field
      rating: actualRating, // Use calculated rating from Reviews table
      reviewCount: actualReviewCount, // Use actual review count from Reviews table
      deliveryTime: baseTime.toString(),
      deliveryTimeRange: timeRange,
      preparationTimeMinutes: business.preparation_time_minutes,
      preparationTime: prepTime.toString() + " min",
      deliveryFee: business.delivery_fee || 2.0,
      deliveryFeeFormatted: formattedDeliveryFee,
      minimum_order_amount: business.minimum_order_amount || 15.00,
      opening_hours: business.opening_hours || {
        monday: { open: "11:00", close: "22:00" },
        tuesday: { open: "11:00", close: "22:00" },
        wednesday: { open: "11:00", close: "22:00" },
        thursday: { open: "11:00", close: "22:00" },
        friday: { open: "11:00", close: "22:00" },
        saturday: { open: "12:00", close: "23:00" },
        sunday: { open: "12:00", close: "23:00" }
      },
      hygiene_rating: business.hygiene_rating || 4.5,
      last_inspection_date: business.last_inspection_date || 'January 2023',
      allergen_info: business.allergen_info || [],
      isNew: false,
      offer: null,
      location: business.location,
      coordinates: business.coordinates,
      delivery_radius: business.delivery_radius,
      delivery_fee_model: business.delivery_fee_model || 'fixed',
      delivery_fee_per_km: business.delivery_fee_per_km || 0,
      businessType: businessType.slug,
      businessTypeName: businessType.name,
      phone: business.phone || '************',
      description: business.description,
      menuCategories: menuCategories,
      deliveryAvailable: business.delivery_available !== false, // Use actual database value, default to true if null/undefined
      status: business.status || 'active', // Include business status
      isPlaceholder: business.status === 'placeholder', // Helper flag for placeholder businesses
      slimMenu: business.slim_menu || false, // Controls whether to show slim product cards (name only) or full cards
      page_layout: business.page_layout || 'standard' // Include page layout for aisle vs standard layout
    };



    // Add type-specific properties based on business type
    switch (businessType.slug) {
      case 'restaurant':
        genericBusiness.cuisines = attributes;
        break;
      case 'shop':
        genericBusiness.storeTypes = attributes;
        break;
      case 'pharmacy':
        genericBusiness.pharmacyTypes = attributes;
        break;
      case 'cafe':
        genericBusiness.cafeTypes = attributes;
        break;
      case 'errand':
        genericBusiness.errandTypes = attributes;
        break;
      default:
        genericBusiness.genericTypes = attributes;
    }

    return genericBusiness;
  } catch (error) {
    console.error(`Error in getBusinessById for ${slug}:`, error);
    return null;
  }
}
